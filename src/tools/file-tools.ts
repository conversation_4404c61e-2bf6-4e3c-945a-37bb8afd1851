import { z } from "zod";
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import fs from "fs/promises";
import path from "path";
import { createReadStream } from "fs";
import FormData from "form-data";
import { Document, MorphikConfig } from "../core/types.js";
import { validatePath } from "../core/security.js";
import { searchFiles, getFileStats } from "../core/file-operations.js";
import { makeDirectRequest } from "../core/api-client.js";
import { getMimeType } from "../core/helpers.js";

export function registerFileTools(server: McpServer, config: MorphikConfig) {
 
}