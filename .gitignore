# Dependency directories
node_modules/
jspm_packages/

# Build outputs
build/
dist/
out/
lib/
*.tsbuildinfo

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE and editor files
.idea/
.vscode/
*.swp
*.swo
.DS_Store
.project
.classpath
.settings/

# Coverage directory used by tools like istanbul
coverage/
.nyc_output/

# Cache directories
.npm/
.eslintcache
.stylelintcache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# TypeScript cache
*.tsbuildinfo

# Temporary files
tmp/
temp/


mcp-docs.md
CLAUDE.md
openapi.json
mcp-typescript-sdk.txt

# Claude context files
flatten-for-claude.js
claude-context.txt